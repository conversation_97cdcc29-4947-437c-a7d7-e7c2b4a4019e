target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### IntelliJ IDEA ###
.idea/modules.xml
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/libraries/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store
/out/
/src/main/resources/pdfFont/
/web/db/
.idea/.gitignore
.idea/AugmentWebviewStateStore.xml
.idea/dataSources.local.xml
.idea/dataSources.xml
.idea/encodings.xml
.idea/misc.xml
.idea/sqldialects.xml
.idea/uiDesigner.xml
.idea/vcs.xml
.idea/workspace.xml
.idea/artifacts/TableConfirm_Web_exploded.xml
.idea/artifacts/TableConfirm.xml
.idea/codeStyles/codeStyleConfig.xml
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c.xml
.idea/dataSources/db4b1f5c-c3ff-4640-8b64-4926ce7507be.xml
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c/storage_v2/_src_/schema/PUBLIC.aaZQjQ.meta
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c/storage_v2/_src_/schema/SYS.rUIBAA.meta
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c/storage_v2/_src_/schema/SYSTEM.L2avkg.meta
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c/storage_v2/_src_/schema/THINGWORX.7DSbfA.meta
.idea/dataSources/23808327-e986-408a-9418-b8a5949cb11c/storage_v2/_src_/schema/THINGWORX.7DSbfA.zip
.idea/dataSources/db4b1f5c-c3ff-4640-8b64-4926ce7507be/storage_v2/_src_/schema/main.uQUzAA.meta
.idea/inspectionProfiles/Project_Default.xml
